# -*- coding: utf-8 -*-
"""
配置文件
"""
import os

NAME = "booking-edi-spider"

PROJECT_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
PLUGIN_JS = os.path.join(PROJECT_PATH, "docs", "stealth.min.js")        # 消除selenium标记

# 录屏开关
RECORD_STATE = True if os.getenv("RECORD_STATE", "false") == "true" else False
# 提交开关
SUBMIT_STATE = True if os.getenv("SUBMIT_STATE", "false") == "true" else False

SERVER_ENV = os.getenv('SERVER_ENV', 'dev')

ENV_PREFIX = {'beta': 'beta-', 'dev': 'dev-', 'local': 'dev-', 'prod': '', "uat": "uat-"}.get(SERVER_ENV, 'dev-')  # 接口前缀

# redis配置
REDIS_CONFIG = {
    "dev": {"host": "dev-middle.hgj.net", "port": 6379, "password": "123456", "db": 1, "max_connections": 1},
    "beta": {"host": "beta-middle.hgj.net", "port": 6379, "password": "123456", "db": 1, "max_connections": 1},
    "prod": {"host": "************", "port": 6379, "password": "ahjlub7nk%wRosR0", "db": 0, "max_connections": 1}
}.get(SERVER_ENV)
# mysql 配置
MYSQL_CONFIG = {
    "local": {"host": "dev-middle.hgj.net", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"},
    "dev": {"host": "dev-middle.hgj.net", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"},
    "beta": {"host": "beta-middle.hgj.net", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"},
    "uat": {"host": "*************", "port": 3306, "user": "spider", "password": "spider", "db": "spider-service", "charset": "utf8"},
    "prod": {"host": "*************", "port": 3306, "user": "spider_service", "password": "fyie6Ev+S9bfmhbu", "db": "spider-service", "charset": "utf8"}}.get(SERVER_ENV)
# 斐斐打码 配置
FFDM_CONFIG = {"pd_id": "134262", "pd_key": "eRf6SGxMIklVipO+hY6RHrKMKq7uPD9S", "app_id": "334262", "app_key": "EVnmNMc9chmOCjtvxwBHU3E1AQSD4v+0"}
HH_ACCOUNT_CONFIG = {"username": "HGJ", "password": "Hgj@121212"}
WHL_ACCOUNT_CONFIG = {"username": "D735", "password": "SqVK12rY"}
# NAS上传api
NAS_URL = {
    "dev": "https://iter-apisix.hgj.com/whale-nas-manager-server/access/upload/security/v1",
    "beta": "https://beta-apisix.hgj.com/whale-nas-manager-server/access/upload/security/v1",
    "uat": "https://uat-file-ingress.hgj.com/whale-nas-manager-server/access/upload/security/v1",
    "prod": "https://file-ingress.hgj.com/whale-nas-manager-server/access/upload/security/v1"
  }.get(SERVER_ENV)
# NAS下载api
NAS_DOWNLOAD_URL = {
    "dev": "https://iter-apisix.hgj.com/whale-nas-manager-server/pass/proxy/",
    "beta": "https://beta-apisix.hgj.com/whale-nas-manager-server/pass/proxy/",
    "uat": "https://uat-file-ingress.hgj.com/whale-nas-manager-server/pass/proxy/",
    "prod": "https://file-ingress.hgj.com/whale-nas-manager-server/pass/proxy/"
  }.get(SERVER_ENV)
# 视频链接
NAS_PREVIEW_URL = f'https://{ENV_PREFIX}fileview.hgj.com/onlinePreview?url='
# rocketmq
ROCKET_MQ_CONFIG = {
    "local": "dev-middle.hgj.net:9876",
    "dev": "dev-middle.hgj.net:9876",
    "beta": "beta-rmq.hgj.net:9876",
    "uat": "172.17.16.39:9876",
    "prod": "rmq2.hgj.net:9876;rmq3.hgj.net:9876;rmq4.hgj.net:9876"
  }.get(SERVER_ENV)

MQ_SUBSCRIBE_ID = "booking-edi-spider"
MQ_TOPIC = 'smart-booking-python-booking-topic'     # 订阅topic，获取消息
MQ_SUBSCRIBE_TAG = "hh-edi-to-python || whl-edi-to-python || python-do-booking-qd-nb-wy"               # 订阅的tag

MQ_PRODUCER_ID = 'booking-spider'  # 生产者

MQ_CREATE_BILL_NO_TAG = 'hh-edi-to-python-result'  # 回填提单号的tag
MQ_UPDATE_BILL_NO_TAG = 'python-to-booking-bill-no-update'  # 回填船公司提单号


# aksk
AKSK = {
    "video": {
        "dev": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "beta": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "prod": {"ACCESS_KEY": "fae4d6825c3f43959e5fb539067bc032", "SECRET_KEY": "b5bb662857c752d88c3c5d5c4064c822e17c074e94d5c47f4c8689281d5189c5"},
    }.get(SERVER_ENV, "dev"),
    "booking": {
        "dev": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "beta": {"ACCESS_KEY": "a547f585d9df4104b5812771ec0f1dce", "SECRET_KEY": "5678b65d314670aa476f61221f568c9fb1c254e0a1feb23719ad0fd751a1d044"},
        "prod": {"ACCESS_KEY": "87a7db5f199a4be4ac03d2473f57b59a", "SECRET_KEY": "9c859cf8c7e4dad0fc38b5fb7bc8c3e65326ed70508c72bf96bbee96fa51e60f"},
    }.get(SERVER_ENV, "dev")
}

# 错误预警群
DEVELOPER_NOTIFY_API = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ec616b74-b95c-4c59-b46d-abefe95972be"
# 订舱组
BOOKING_GROUP_API = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c76c7c8c-29d6-4e48-8750-81d3eb026745"
# gunicorn配置文件路径，方便将不是里外启动的服务日志输出
GUNICORN_FILENAME = os.path.join(PROJECT_PATH, "gunicorn_log.py")

# 报文文件夹
EDI_DIR = os.path.join(PROJECT_PATH, "docs", "edi")
# 报文结果上传地址
EDI_UPLOAD_RESULT_URL = {
    "dev": "https://dev-apisix.hgj.com/booking-common-channel/aksk/auto/autoExecuteResultCallBackNotify",
    "beta": "https://beta-apisix.hgj.com/booking-common-channel/aksk/auto/autoExecuteResultCallBackNotify",
    "prod": "http://ingress-ng.hgj.com/booking-common-channel/aksk/auto/autoExecuteResultCallBackNotify"
}.get(SERVER_ENV)
# 上传数据到CHIPMUNK
EDI_UPLOAD_TO_CHIPMUNK_RESULT_URL = {
    "dev": "https://dev-apisix.hgj.com/chipmunk-go/aksk/auto/autoExecuteResultCallBackNotify",
    "beta": "https://beta-apisix.hgj.com/chipmunk-go/aksk/auto/autoExecuteResultCallBackNotify",
    "prod": "http://ingress-ng.hgj.com/chipmunk-go/aksk/auto/autoExecuteResultCallBackNotify"
}.get(SERVER_ENV)