import os
import time
import json
import base64
from logging import Logger
from urllib.parse import quote

import cv2
import requests
from retry.api import retry_call
from selenium.webdriver.common.by import By

from utils import logger
from utils.util_ffdm import FFDMApi
from utils.util_operate_mysql import MysqlUtil
from utils import send_request, send_message_to_developer
from utils import create_authorization
from conf import NAS_URL, NAS_DOWNLOAD_URL, NAS_PREVIEW_URL, PROJECT_PATH, MYSQL_CONFIG, FFDM_CONFIG, NAME


class Base:
    """扩展：制作视频，截图，推送"""
    name = ""
    # 睡眠时间 秒
    short_time = 0.2
    mid_time = 0.5
    long_time = 1

    def __init__(self, data):
        self.data = data
        self.params = Params()
        self.is_not_need_retry = False                   # 标记是否需要重试
        self.logger: Logger = logger                    # 创建logger
        self.util_mysql = MysqlUtil(**MYSQL_CONFIG)     # 连接mysql
        self.ff = FFDMApi(**FFDM_CONFIG)                # 初始化斐斐打码
        self.register_signal()
        self.insert_id = self.insert_sql_data()

    @staticmethod
    def register_signal(): ...

    def insert_sql_data(self):
        """持久化数据"""
        sql = "INSERT INTO hh_edi_booking_info (carrierCode, delegationNo, data, bookingStartTime) VALUES (%s, %s, %s, %s)"
        insert_info = self.util_mysql.update(sql, params=(self.name, self.data["detail"]["delegationNo"], json.dumps(self.data), time.strftime("%Y-%m-%d %H:%M:%S")))
        return insert_info[-1]

    def check_data(self, *args, **kwargs):
        raise NotImplementedError(f'{self.__class__.__name__}.check_data callback is not defined')

    def before_insert(self):
        raise NotImplementedError(f'{self.__class__.__name__}.before_insert callback is not defined')

    def insert_data(self, *args, **kwargs):
        raise NotImplementedError(f'{self.__class__.__name__}.insert_data callback is not defined')

    def after_insert(self):
        raise NotImplementedError(f'{self.__class__.__name__}.after_insert callback is not defined')

    def handle_result(self, *args, **kwargs):
        raise NotImplementedError(f'{self.__class__.__name__}.handle_result callback is not defined')

    def main(self, *args, **kwargs):
        try:
            self.logger.info(f"{self.data['messageId']}-{self.params.delegationNo}开始执行")
            # 校验数据
            self.check_data(*args, **kwargs)
            # 插入数据前钩子
            self.before_insert()
            # 填充数据
            self.insert_data(*args, **kwargs)
            # 插入数据后钩子
            self.after_insert()
            # 处理结果
            not self.params.is_exist and self.handle_result(*args, **kwargs)
            self.logger.info(f"{self.data['messageId']}-{self.params.delegationNo}结束执行")
        except Exception as e:
            logger.error(e, exc_info=True)
            send_message_to_developer(f"{self.params.carrierCode}-{self.params.delegationNo}订舱失败", ["17826806070"])


class SeleniumOperateMixin:

    def __init__(self):
        self.logger = logger

    @staticmethod
    def check_document_ready_state(browser, time_duration=5):
        start_time = time.time()
        while True:
            if browser.execute_script("return document.readyState") == "complete" or (time.time() - start_time) > time_duration:
                break

    def clear_and_send_keys(self, browser, by: str, rule: str, msg):
        """
        点击并发送
        每次都获取是因为有时旧的元素定位不到会报错，所以每次都重新获取元素并完成相应操作
        :param browser: 浏览器对象
        :param by: 通过什么方式定位，如： id，xpath，css_selector
        :param rule: 定位的规则
        :param msg: 需要输入的值
        :return:
        """
        by = by.lower()
        self.__get_element(browser, by, rule).clear()
        time.sleep(0.1)
        self.__get_element(browser, by, rule).send_keys(msg)
        time.sleep(0.2)

    def click_by_js(self, browser, by, rule):
        """通过js点击"""
        by = by.lower()
        ele = self.__get_element(browser, by, rule)
        browser.execute_script("arguments[0].click();", ele)
        time.sleep(0.2)

    def scroll_to_ele(self, browser, by, rule):
        """滚动到某个元素的位置"""
        ele = self.__get_element(browser, by, rule)
        js4 = "arguments[0].scrollIntoView();"
        browser.execute_script(js4, ele)
        return True

    def __get_element(self, browser, by, value):
        """通过不同的方式查找界面元素"""
        element = None
        by = by.lower()
        if hasattr(By, by.upper()):
            element = browser.find_element(by=by, value=value)
        else:
            self.logger.error("无对应方法，请检查")
        return element


class HandleMediaMixin:

    def __init__(self, name):
        self.name = name
        self.logger = logger
        self.screen_flag = True

    @staticmethod
    def request(url, method="get", **kwargs) -> requests.Response:
        """发送请求"""
        default_setting = {"method": method}
        default_setting.update(kwargs)
        response = retry_call(send_request, [url], default_setting, tries=1)
        return response

    def screen_video_picture(self, browser, delegation_no):
        """视频截图"""
        screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', f'{self.name}_{delegation_no}')
        if os.path.exists(screenshot_png_path): os.system(f'rm -rv {screenshot_png_path}')
        os.mkdir(screenshot_png_path)
        index = 1
        while True:
            if not self.screen_flag: return True
            file_path = os.path.join(screenshot_png_path, f'{index}.png')
            browser.save_screenshot(file_path)
            index += 1
            time.sleep(0.1)

    def generate_video(self, delegation_no):
        """生成视频"""
        screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', f'{self.name}_{delegation_no}')
        if not os.path.exists(screenshot_png_path) or not os.listdir(screenshot_png_path): return "", ""
        filelist = os.listdir(screenshot_png_path)
        filelist.sort(key=lambda x: int(x[:-4]))
        avi_path = os.path.join(PROJECT_PATH, 'docs', f"{self.name}_{delegation_no}.avi")
        mp4_path = os.path.join(PROJECT_PATH, 'docs', f"{self.name}_{delegation_no}.mp4")
        # 获取并设置尺寸
        img = cv2.imread(os.path.join(screenshot_png_path, filelist[0]), 3)
        fps = 10
        size = (img.shape[1], img.shape[0])
        video = cv2.VideoWriter(avi_path, cv2.VideoWriter_fourcc('I', '4', '2', '0'), fps, size)
        # 视频保存在当前目录下
        for picture_name in filelist:
            file_path = os.path.join(screenshot_png_path, picture_name)
            img = cv2.imread(file_path)
            video.write(img)
        video.release()

        # 压缩视频
        os.system(f'ffmpeg -i {avi_path} {mp4_path}')
        filename = os.path.join(PROJECT_PATH, 'docs', f"{self.name}_{delegation_no}.mp4")
        with open(filename, "rb") as f:
            try:
                download_url, preview_url = self.upload_media(f"{self.name}_{delegation_no}.mp4", f)
            except Exception as e:
                self.logger.error(e, exc_info=True)
                message = "".join([self.name, "文件上传失败！！！", "-"*30, "\n", str(e)])
                send_message_to_developer(message, [])
            finally:
                os.path.exists(filename) and os.system(f'rm {filename}')
        # 删除文件
        self.remove_useless_file(delegation_no=delegation_no)
        return download_url, preview_url

    def upload_media(self, media_name, text_io_wrapper):
        """上传媒体文件"""
        download_url, preview_url = "", ""
        try:
            payload = {'type': NAME, 'relevanceId': media_name.rsplit(".", 1)[0], 'downloadFileName': media_name}
            files = [('multipartFile', (media_name, text_io_wrapper, 'application/octet-stream'))]
            headers = {'appname': NAME, "authorization": create_authorization("video")}
            json_data = self.request(NAS_URL, method="post", headers=headers, data=payload, files=files).json()
            data_id = json_data.get('data', '')
            if data_id:
                download_url = NAS_DOWNLOAD_URL + data_id
                preview_url = NAS_PREVIEW_URL + quote(base64.b64encode(download_url.encode()))
        except Exception as e:
            self.logger.error(e, exc_info=True)

        return download_url, preview_url

    def remove_useless_file(self, delegation_no):
        """删除无用文件"""
        screenshot_png_path = os.path.join(PROJECT_PATH, 'docs', f'{self.name}_{delegation_no}')
        if not os.path.exists(screenshot_png_path): return
        avi_path = os.path.join(PROJECT_PATH, 'docs', f"{self.name}_{delegation_no}.avi")
        mp4_path = os.path.join(PROJECT_PATH, 'docs', f"{self.name}_{delegation_no}.mp4")
        # 删除文件夹
        os.path.exists(screenshot_png_path) and os.system(f'rm -rf {screenshot_png_path}')
        os.path.exists(avi_path) and os.system(f'rm {avi_path}')
        os.path.exists(mp4_path) and os.system(f'rm {mp4_path}')


class Params:
    __slots__ = ("billNo", "carrierCode", "delegationNo", "draftState", "is_exist", "searchResult", "message",
                 "error_info_system_list", "error_info_input_list", "error_info_check_list", "website", "websiteName",
                 "download_url", "preview_url")

    def __init__(self):
        self.billNo = ""
        self.carrierCode = ""
        self.delegationNo = ""
        self.draftState = False
        self.is_exist = False
        self.searchResult = ""
        self.error_info_system_list = []                                        # 保存主流程错误信息
        self.error_info_input_list = []                                         # 保存输入错误信息
        self.error_info_check_list = []                                         # 保存校验错误信息
        self.website = "https://hhdc.chinasailing.com.cn/home"
        self.websiteName = "shhh"
        self.message = {}
        self.download_url = ""
        self.preview_url = ""

    @property
    def error_info_list(self):
        return self.error_info_system_list + self.error_info_input_list + self.error_info_check_list

    @property
    def error_detail(self):
        """将错误进行汇总"""
        errors = []
        errors.extend(list(map(lambda x: {"errorType": "SYSTEM_ERROR", "errorContent": x}, set(self.error_info_system_list))))
        errors.extend(list(map(lambda x: {"errorType": "PAGE_INPUT_ERROR", "errorContent": x}, set(self.error_info_input_list))))
        errors.extend(list(map(lambda x: {"errorType": "PAGE_CHECK_ERROR", "errorContent": x}, set(self.error_info_check_list))))
        return errors

    @property
    def params(self):
        params_dict = {}
        for one in self.__slots__:
            if one.startswith("error_info"):
                continue
            params_dict[one] = getattr(self, one)
        return params_dict

