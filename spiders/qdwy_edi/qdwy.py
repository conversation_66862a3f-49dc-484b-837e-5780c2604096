"""
青岛外运（箱满舱）自动订舱：https://www.etransful.com/home.html
需求文档：http://wiki.hgj.net/pages/viewpage.action?pageId=82973378
登录账号：***********
登录密码：Szhgj1234!
"""
import base64
import io
import os
import time
import json
from threading import Thread

import ddddocr
from PIL import Image
from selenium.webdriver import Chrome, ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from spiders.qdwy_edi.config import signing_method_mapping, payment_method_mapping
from spiders import Base, SeleniumOperateMixin, HandleMediaMixin
from utils.util_aes import aes_ecb_decrypt
from utils.util_normal import delete_edi_file, handle_error_message
from utils import create_authorization, logger
from utils.util_selenium import Selenium<PERSON><PERSON><PERSON>pider
from conf import RECORD_STATE, SERVER_ENV, SUBMIT_STATE, EDI_UPLOAD_RESULT_URL


class QDWYSpider(Base, SeleniumOperateMixin, HandleMediaMixin):

    name = "qdwy"

    def __init__(self, data, service, browser):
        Base.__init__(self, data)
        HandleMediaMixin.__init__(self, self.name)
        SeleniumOperateMixin.__init__(self)
        self.msg = data
        self.service = service
        self.browser: Chrome = browser
        self.browser.set_window_size(width=1792, height=1120)
        self.params.delegationNo = data["detail"]["delegationNo"]
        self.params.carrierCode = data["detail"]["carrierCode"]
        self.home_url = "https://www.etransful.com/home.html"
        self.my_order_url = "https://www.etransful.com/index.html#/myOrder"
        self.order_draft_url = "https://www.etransful.com/index.html#/order_draft"
        self.download_execl_api = data["orderFiles"][0]["fileUrl"]
        self.execl_path = "temp.xls"
        self.username = data["accountBean"]["account"]
        self.password = aes_ecb_decrypt((data["recordId"], self.username), self.data["accountBean"]["password"])
        self.wait = WebDriverWait(self.browser, 10, 0.2)
        self.wrapper = ""
        self.request_ids = []
        # 初始化网络监控
        self.setup_network_monitoring()

    def setup_network_monitoring(self) -> None:
        """设置网络监控"""
        try:
            # 启用页面域
            self.browser.execute_cdp_cmd('Page.enable', {})
            self.logger.info("网络监控已启用")
        except Exception as e:
            self.logger.error(f"{ErrorMessages.NETWORK_MONITORING_FAILED}: {e}")

    def check_data(self, *args, **kwargs) -> None:
        """数据校验（保持原有接口）"""
        pass

    def before_insert(self):
        # 根据环境变量开启录屏
        RECORD_STATE and Thread(target=self.screen_video_picture, args=(self.browser, self.params.delegationNo)).start()

    def insert_data(self, *args, **kwargs):
        if not self.login(): return
        if self.check_result():
            self.logger.info(f"委托编号{self.params.delegationNo}已订过舱")
            self.params.error_info_check_list.append(f"委托编号{self.params.delegationNo}已订过舱")
        else:
            if not self.handle_execl(): return
            if not self.download_execl(): return
            if not self.upload_execl(): return
            if not self.insert_info_detail():return
            self.save_screen()
            if self.submit():
                self.check_result()
            else:
                self.save_draft()

    def after_insert(self):
        # 当填入失败时再次截图，方便将漏填字段标红显示保留
        if self.params.error_info_input_list or self.params.error_info_system_list or self.params.error_info_check_list:
            self.save_screen()

    def handle_result(self, *args, **kwargs):
        """处理订舱结果"""
        self.screen_flag = False # 停止录屏
        error_info_list = self.params.error_info_list
        error_info = ", ".join(error_info_list)

        flag = False if error_info_list else True

        if RECORD_STATE:
            self.params.download_url, self.params.preview_url = self.generate_video(self.params.delegationNo)
        else:
            self.params.download_url, self.params.preview_url = self.wrapper and self.upload_media(f"{self.name}_{self.params.delegationNo}.png", self.wrapper) or ("", "")

        if self.data["count"] == 2 and not flag:
            error_info = "".join([error_info, "  连续上传错误俩次"])

        message = {"bookingId": self.data["bookingId"],
                   "recordId": self.data["recordId"],
                   "errorInfo": error_info,
                   "flag": flag,
                   "videoDownloadUrl": self.params.download_url,
                   "videoPreviewUrl": self.params.preview_url,
                   "errors": self.params.error_detail}
        message.update(self.params.params)
        self.params.message = message

        # 返回订舱结果
        self.upload_message("booking", message)
        # 保存订舱结果
        self.save_booking_result()

    @handle_error_message("error_info_system_list", "登陆失败", delay=3, tries=2)
    def login(self):
        """登陆"""
        self.logger.info("开始检查登录状态")
        self.browser.get(self.home_url)
        try:
            self.wait.until(EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'退出')]")))
            self.logger.info("已登录")
            return True
        except:
            self.logger.warning("登陆状态失效，重新登陆", exc_info=False)

        # 点击登录
        login_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//a[contains(text(),'登录')]")))
        login_btn.click()

        # 切换到登录iframe
        iframe_element = self.browser.find_element(By.CLASS_NAME, "login-card")
        self.browser.switch_to.frame(iframe_element)

        # 点击密码登录
        login_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(text(),'密码登录')]")))
        time.sleep(self.long_time)
        login_btn.click()

        # 输入账号密码
        username_input = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='text']")))
        password_input = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='password']")))
        time.sleep(self.long_time)
        username_input.send_keys(self.username)
        time.sleep(self.long_time)
        password_input.send_keys(self.password)

        # 点击登录
        login_btn = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, "//span[contains(text(),'登录')]/ancestor::button[@type='button']")))
        login_btn.click()

        for i in range(3):
            # 处理滑块验证码
            time.sleep(5)
            self.handle_slider_captcha()
            # 校验登录结果
            self.browser.switch_to.default_content()
            try:
                self.wait.until(EC.presence_of_element_located((By.XPATH, "//span[contains(text(),'退出')]")))
                self.logger.info("登录成功")
                return True
            except:
                self.logger.error(f"未查询到‘退出’标记，第{i + 1}次登录失败")
                self.browser.switch_to.frame(iframe_element)
        self.params.error_info_system_list.append("登录失败")
        return False

    def get_captcha_response(self):
        """获取包含验证码图片数据的响应"""
        try:
            # 获取所有网络请求
            network_logs = self.browser.get_log('performance')
            for log in network_logs:
                message = json.loads(log['message'])
                # 只处理网络响应
                if message['message']['method'] == 'Network.responseReceived':
                    response_data = message['message']['params']['response']
                    request_id = message['message']['params']['requestId']
                    # 过滤验证码请求URL
                    if "api-common/iam-eas/v1/common/getImg" in response_data[
                        'url'] and request_id not in self.request_ids:
                        self.request_ids.append(request_id)
                        # 获取响应体
                        response_body = self.browser.execute_cdp_cmd('Network.getResponseBody', {
                            'requestId': request_id
                        })
                        body_content = response_body.get('body', '')
                        if response_body.get('base64Encoded', False):
                            body_content = base64.b64decode(body_content).decode('utf-8')
                        json_data = json.loads(body_content)
                        target_img, background_img = json_data["data"]["imageCaptchaInfo"]["templateImage"], \
                            json_data["data"]["imageCaptchaInfo"]["backgroundImage"]
                        return target_img, background_img
            return False, False
        except Exception as e:
            self.logger.error(f"获取包含验证码图片数据的响应失败: {e}")
            self.params.error_info_system_list.append("获取包含验证码图片数据的响应失败")
            return False, False

    @staticmethod
    def fix_base64_padding(base64_str):
        """修复base64字符串的填充"""
        # 移除可能的前缀
        if base64_str.startswith('data:image'):
            base64_str = base64_str.split(',')[1]
        # 添加必要的填充
        missing_padding = len(base64_str) % 4
        if missing_padding:
            base64_str += '=' * (4 - missing_padding)
        return base64_str

    @handle_error_message("error_info_system_list", "滑块验证码处理失败", delay=5, tries=2)
    def handle_slider_captcha(self):
        """处理滑块验证码"""
        target_img, background_img = self.get_captcha_response()
        if not target_img or not background_img:
            raise Exception
        # 获取滑块按钮
        slider = self.browser.find_element(By.CLASS_NAME, "el-icon-s-grid")
        # 计算距离
        target_img_bytes = base64.b64decode(self.fix_base64_padding(target_img))
        background_img_bytes = base64.b64decode(self.fix_base64_padding(background_img))
        background_image = Image.open(io.BytesIO(background_img_bytes))
        bg_width = background_image.width
        det = ddddocr.DdddOcr(det=False, ocr=False)
        res = det.slide_match(target_img_bytes, background_img_bytes)
        estimated_distance = res["target"][0] * 295 / bg_width

        # 无轨迹校验
        ActionChains(self.browser).click_and_hold(slider).perform()
        ActionChains(self.browser).move_by_offset(xoffset=estimated_distance, yoffset=0).perform()
        time.sleep(1)
        ActionChains(self.browser).release().perform()

        time.sleep(3)

    @handle_error_message("error_info_system_list", "上传execl失败", delay=3, tries=2)
    def handle_execl(self):
        """处理execl上传流程"""
        self.logger.info("开始execl上传流程")
        self.browser.get(self.home_url)
        # 点击在线订舱
        online_booking = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//a[@ng-show='bookingAuth==1']")))
        online_booking.click()

        # 选择子公司
        sub_company_btn = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, ".//button[@data-id='sub-company-id']")))
        sub_company_btn.click()
        sub_company_option = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//span[text()='华中集海']")))
        sub_company_option.click()

        # 选择船公司
        ship_company_btn = self.wait.until(
            EC.element_to_be_clickable((By.XPATH, ".//button[@data-id='ship-company-id']")))
        ship_company_btn.click()
        ship_company_option = self.wait.until(EC.element_to_be_clickable((By.XPATH, f".//ul[@role='menu']//span[contains(text(),'{self.params.carrierCode}')]")))
        ship_company_option.click()

        # 点击订舱按钮
        booking_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//input[@value='订舱']")))
        booking_btn.click()

        # 等待页面加载完成
        self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")


    @handle_error_message("error_info_system_list", "下载execl失败", delay=1, tries=2)
    def download_execl(self):
        """下载execl"""
        self.logger.info("开始下载execl")
        response = self.request(url=self.download_execl_api, method="get")
        if response.status_code == 200 and response.headers.get("Content-Type", "").startswith("application/vnd.openxml"):
            self.logger.info("下载execl成功")
            with open(self.execl_path, "wb") as f:
                f.write(response.content)
            return True
        else:
            self.logger.error(f"下载execl失败，返回状态码：{response.status_code}，返回内容：{response.content.decode('utf-8')}")
            self.params.error_info_system_list.append("下载execl失败")
            return False

    @handle_error_message("error_info_system_list", "上传execl失败", delay=1, tries=2)
    def upload_execl(self):
        """上传execl"""
        self.logger.info("开始上传execl")
        absolute_execl_path = os.path.abspath(self.execl_path)
        # 检查本地文件是否存在
        if not os.path.exists(absolute_execl_path):
            self.logger.error("保存execl失败")
            self.params.error_info_system_list.append("保存execl失败")
            return False

        # 查找文件上传元素
        file_input = self.wait.until(EC.presence_of_element_located((By.ID, "fillExcelImport")))
        # 上传文件
        file_input.send_keys(absolute_execl_path)
        time.sleep(self.long_time*5)
        if self.wait.until(EC.element_to_be_clickable((By.ID, "EBBO_CONSIGN_NO"))).get_attribute("value"):
            self.logger.info("上传execl成功")
            return True
        else:
            self.logger.error(f"上传execl失败")
            self.params.error_info_system_list.append("上传execl失败")
            return False

    @handle_error_message("error_info_input_list", "填写订舱详细信息异常", delay=1)
    def insert_info_detail(self):
        """填写订舱详细信息"""
        self.logger.info("开始填写订舱详细信息")
        # 收发通信息
        shipper_name = self.msg["detail"]["bookingOrderInfoBean"]["shipperContactInfoBean"]["shipperName"]
        shipper_address = self.msg["detail"]["bookingOrderInfoBean"]["shipperContactInfoBean"]["shipperAddress"]
        consignee_name = self.msg["detail"]["bookingOrderInfoBean"]["consigneeContactInfoBean"]["consigneeName"]
        consignee_address = self.msg["detail"]["bookingOrderInfoBean"]["consigneeContactInfoBean"]["consigneeAddress"]
        notify_name = self.msg["detail"]["bookingOrderInfoBean"]["notifyContactInfoBean"]["notifyName"]
        notify_address = self.msg["detail"]["bookingOrderInfoBean"]["notifyContactInfoBean"]["notifyAddress"]
        second_notify_name = self.msg["detail"]["bookingOrderInfoBean"]["secondNotifyContactInfoBean"]["secondNotifyName"]
        second_notify_address = self.msg["detail"]["bookingOrderInfoBean"]["secondNotifyContactInfoBean"]["secondNotifyAddress"]

        etd = self.msg["detail"]["bookingOrderInfoBean"]["sailingScheduleInfoBean"]["etd"] # 预计开船日期
        signing_way = signing_method_mapping[self.msg["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]["signingMethod"]] # 签单方式
        bill_number = self.msg["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]["billFNum"] # 提单份数
        payment_way = payment_method_mapping[self.msg["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]["paymentMethod"]] # 付款方式
        transport_clause = self.msg["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]["transportClause"].replace("_", "/") # 运输条款
        hbl_flag =  "是" if self.msg["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]["oneselfSendingFlag"] == "HGJ" else "否" # 是否HBL
        freeze_replace_dry_flag = self.msg["bookingExtendData"]["nonOperatingReefer"] # 是否冻代干
        en_product_name = self.msg["detail"]["bookingOrderInfoBean"]["cargoInfoBeanList"][0]["englishProductName"] # 英文品名
        cn_product_name = self.msg["detail"]["bookingOrderInfoBean"]["cargoInfoBeanList"][0]["chineseProductName"] # 中文品名
        soc_flag = self.msg["detail"]["bookingOrderInfoBean"]["containerInfoBeanList"][0]["soc"] # SOC标记

        if self.params.carrierCode == "MSC":
            # 填写发货人信息
            self.wait.until(EC.element_to_be_clickable((By.ID, "a11ArrowId"))).click()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a11-id']//textarea[@id='EBCU_NAME']"))).send_keys(shipper_name)
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a11-id']//textarea[@id='EBCU_ADDRESS']"))).send_keys(shipper_address)
            # 填写收货人信息
            self.wait.until(EC.element_to_be_clickable((By.ID, "a12ArrowId"))).click()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a12-id']//textarea[@id='EBCU_NAME']"))).send_keys(consignee_name)
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a12-id']//textarea[@id='EBCU_ADDRESS']"))).send_keys(consignee_address)
            # 填写通知人信息
            self.wait.until(EC.element_to_be_clickable((By.ID, "a13ArrowId"))).click()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a13-id']//textarea[@id='EBCU_NAME']"))).send_keys(notify_name)
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a13-id']//textarea[@id='EBCU_ADDRESS']"))).send_keys(notify_address)
            # 填写第二通知人信息
            self.wait.until(EC.element_to_be_clickable((By.ID, "a14ArrowId"))).click()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a14-id']//textarea[@id='EBCU_NAME']"))).send_keys(second_notify_name)
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//div[@id='a14-id']//textarea[@id='EBCU_ADDRESS']"))).send_keys(second_notify_address)

        # 开船日期
        departure_date_ele = self.wait.until(EC.element_to_be_clickable((By.ID, "EBBO_DEPARTURE_DATE")))
        departure_date_ele.clear()
        departure_date_ele.send_keys(etd)
        departure_date_ele.click()

        # 签单方式
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@data-id='EBBO_RELEASE_BL_TYPE']"))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, f"//ul//span[contains(text(),'{signing_way}')]"))).click()

        # 提单份数
        bill_number_ele = self.wait.until(EC.element_to_be_clickable((By.ID, "EBBO_BL_NUM")))
        bill_number_ele.clear()
        bill_number_ele.send_keys(bill_number)

        # 付款方式
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@data-id='EBBO_PAYMODE_ID']"))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, f"//ul//span[contains(text(),'{payment_way}')]"))).click()

        # 运输条款
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@data-id='EBBO_TERM_ID']"))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, f"//ul//span[contains(text(),'{transport_clause}')]"))).click()

        # 是否HBL
        self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@data-id='EBBO_HBL_FLAG']"))).click()
        self.wait.until(EC.element_to_be_clickable((By.XPATH, f"//ul//span[contains(text(),'{hbl_flag}')]"))).click()

        # 是否冻代干
        if freeze_replace_dry_flag:
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@class='freeze-chb-css-id']"))).click()

        # 主要品名
        if self.params.carrierCode == 'MSC':
            main_name_ele = self.wait.until(EC.element_to_be_clickable((By.ID, "EBCA_MAIN_NAME")))
            main_name_ele.clear()
            main_name_ele.send_keys(en_product_name)

        # 中文货名
        if cn_product_name:
            cn_name_ele = self.wait.until(EC.element_to_be_clickable((By.ID, "EBCA_CARGO_DESCRIPTION_CN")))
            cn_name_ele.clear()
            cn_name_ele.send_keys(cn_product_name)

        # SOC标记
        if soc_flag:
            self.wait.until(EC.element_to_be_clickable((By.ID, "EBCO_SOC_FLAG"))).click()

        # 目的地相关
        if transport_clause == 'CY-DOOR' and self.params.carrierCode == 'MSC':
            # 目的地属性
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@data-id='EBBO_DESTINATION_PROPERTY']"))).click()
            destination_property = self.msg["detail"]["bookingOrderInfoBean"]["additionalInfoBean"]["destinationProperty"]
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//ul//span[contains(text(),'{destination_property}')]"))).click()
            # 目的地运输方式
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@data-id='EBBO_DESTINATION_TRAFFIC']"))).click()
            destination_traffic_way = self.msg["detail"]["bookingOrderInfoBean"]["additionalInfoBean"]["destinationTransportationType"]
            self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//ul//span[contains(text(),'{destination_traffic_way}')]"))).click()


    def submit(self):
        """提交订舱"""
        try:
            self.logger.info("开始提交订舱")
            sava_draft_flag = self.msg["bookingExtendData"]["commitDraft"] # 是否保存草稿
            if sava_draft_flag:
                self.logger.info("含危险品，仅支持保存草稿")
                self.params.error_info_check_list.append("含危险品，仅支持保存草稿")
                self.is_not_need_retry = True
                return False
            elif SERVER_ENV == "prod" and SUBMIT_STATE:
                # 提交
                self.browser.find_element(By.XPATH, "//div[@ng-show='orderFlag==null || orderFlag==6']/button[contains(text(),'提交订舱')]").click()
                time.sleep(3)
                # 等待页面跳转到我的订单页
                self.wait.until(EC.url_to_be(self.my_order_url))
                self.is_not_need_retry = True
                self.logger.info("提交订舱成功")
                return True
            else:
                self.logger.info("非生产环境，仅支持保存草稿")
                self.params.error_info_check_list.append("非生产环境，仅支持保存草稿")
                self.is_not_need_retry = True
                return False
        except Exception as e:
            self.logger.error(f"提交订舱失败: {e}", exc_info=True)
            self.params.error_info_input_list.append("提交订舱失败")
            return False

    def save_draft(self):
        """保存草稿"""
        try:
            self.logger.info(f"开始保存草稿")
            # 保存草稿
            self.browser.find_element(By.XPATH,"//div[@ng-show='orderFlag==null || orderFlag==6']/button[contains(text(),'保存草稿')]").click()
            time.sleep(3)
            # 等待页面跳转到订单草稿页
            self.wait.until(EC.url_to_be(self.order_draft_url))
            self.logger.info(f"保存草稿成功")
        except Exception as e:
            self.logger.error(f"保存草稿失败: {e}", exc_info=True)
            self.params.error_info_input_list.append(f"保存草稿失败")
            self.is_not_need_retry = False

    def upload_message(self, api_type, pyload: dict) -> bool:
        """
        将数据返回到订舱
        :param api_type: 接口类型
        :param pyload: 数据
        :return: 上传成功为True，否则为False
        """
        headers = {"authorization": create_authorization(api_type)}
        response = self.request(url=EDI_UPLOAD_RESULT_URL, method="post", headers=headers, data=pyload,
                                topic="json")
        if response.status_code == 200 and response.json()["code"] == 200:
            self.logger.info("上传订舱结果成功")
            return True
        return False

    def save_booking_result(self):
        try:
            sql = "UPDATE hh_edi_booking_info set mediaDownloadLink=%s, mediaPreviewLink=%s, bookingEndTime=%s, message=%s where id=%s"
            self.util_mysql.update(sql, params=(self.params.download_url, self.params.preview_url, time.strftime("%Y-%m-%d %H:%M:%S"), json.dumps(self.params.message, ensure_ascii=False), self.insert_id))
        except Exception as e:
            self.logger.error(e, exc_info=True)

    @handle_error_message("error_info_check_list", "订舱结果查询失败", delay=3, tries=2, exc_info=False)
    def check_result(self):
        """检查订舱结果"""
        self.logger.info("开始检查订舱结果")
        self.browser.get("https://www.etransful.com/index.html#/myOrder")

        # 清空委托单号输入框并输入
        delegation_ele = self.wait.until(EC.element_to_be_clickable((By.ID, "EBBO_CONSIGN_NO")))
        delegation_ele.clear()
        delegation_ele.send_keys(self.params.delegationNo)

        # 点击查询按钮
        search_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//button[@ng-click='searchClick()']")))
        search_btn.click()
        time.sleep(3)

        try:
            WebDriverWait(self.browser, 3, 0.2).until(EC.presence_of_element_located((By.XPATH, f"//div[@ng-repeat='p in orderArr'][1]/div[1]/div[2]/tooltip[@tooltip-template='{self.params.delegationNo}']")))
        except:
            raise Exception(f"未查询到订舱结果")
        booking_status_ele = self.wait.until(EC.presence_of_element_located((By.XPATH, f"//div[@ng-repeat='p in orderArr'][1]/div[2]/div[5]/span")))
        booking_status = booking_status_ele.text
        # 无论哪种状态都意味着已订舱
        if booking_status == "已提交":
            self.logger.info("订舱结果查询成功，订单状态为：已提交")
        else:
            self.logger.warning(f"订舱结果查询成功，订单状态异常：{booking_status}")
        return True


    def save_screen(self):
        """填入完成后截取全屏"""
        self.logger.info("订舱信息填写完毕，开始截取页面截图")
        try:
            self.wrapper and self.wrapper.close()
            if "提交订舱" in self.browser.page_source and EC.visibility_of_element_located((By.XPATH, "//button[contains(text(), '提交订舱')]"))(self.browser):
                height = self.browser.find_element(By.XPATH, "/html/body/div[2]/div").size["height"]
                self.browser.set_window_size(width=self.browser.get_window_size()["width"], height=height)
                p = self.browser.get_screenshot_as_png()
                self.wrapper = io.BytesIO(p)
                self.browser.set_window_size(width=1792, height=1120)
            else:
                p = self.browser.find_element(By.CSS_SELECTOR, "html").screenshot_as_png
                self.wrapper = io.BytesIO(p)
        except Exception as e:
            self.logger.error(f"截图失败: {e}", exc_info=True)
            self.wrapper = ""

    def __del__(self):
        self.screen_flag = False
        # 删除execl文件
        delete_edi_file(self.execl_path)
        # 清空之前的性能日志
        self.browser.get_log('performance')


if __name__ == '__main__':
    selenium_obj = SeleniumBaseSpider(logger)
    # 是否需要开启无头模式，默认是开启
    selenium_obj.headless = False
    msg = {
    "bookingId": "1132620667830528",
    "detail": "{\"bookingId\":\"1132620667830528\",\"businessBookingId\":\"1132620667830528\",\"businessPortCode\":\"QINGDAO\",\"portNameEn\":\"QINGDAO\",\"delegationNo\":\"***************\",\"billNo\":\"QD123456\",\"masterBillNo\":\"QD123456\",\"carrierCode\":\"MSC\",\"carrierNameEn\":\"MSC\",\"carrierNameCn\":\"地中海\",\"bookingStatus\":\"SENT_TO_SHIPPING_COMPANY_FOR_RELEASE\",\"bookingOrderInfoBean\":{\"shipperContactInfoBean\":{\"shipperName\":\"ERIC-TEST-VALIDE\",\"shipperAddress\":\"1\",\"shipperCountry\":null,\"shipperCountryCode\":null,\"shipperProvince\":null,\"shipperProvinceCode\":null,\"shipperCity\":null,\"shipperCityCode\":null,\"shipperPostcode\":null},\"consigneeContactInfoBean\":{\"consigneeName\":\"22\",\"consigneeAddress\":\"BOULEVARD SANCHEZ TABOADA NO. \\n10133INT. 305 COL. REVOLUCION \\nTIJUANA,BAJA CALIFORNIA CP. 22##\",\"consigneeCountry\":null,\"consigneeCountryCode\":null,\"consigneeProvince\":null,\"consigneeProvinceCode\":null,\"consigneeCity\":null,\"consigneeCityCode\":null,\"consigneePostcode\":null},\"notifyContactInfoBean\":{\"notifyName\":\"22\",\"notifyAddress\":\"BOULEVARD SANCHEZ TABOADA NO. \\n10133INT. 305 COL. REVOLUCION \\nTIJUANA,BAJA CALIFORNIA CP. 22##\",\"notifyCountry\":null,\"notifyCountryCode\":null,\"notifyProvince\":null,\"notifyProvinceCode\":null,\"notifyCity\":null,\"notifyCityCode\":null,\"notifyPostcode\":null},\"secondNotifyContactInfoBean\":{\"secondNotifyName\":\"22\",\"secondNotifyAddress\":\"BOULEVARD SANCHEZ TABOADA NO. \\n10133INT. 305 COL. REVOLUCION \\nTIJUANA,BAJA CALIFORNIA CP. 22##\",\"secondNotifyCountry\":null,\"secondNotifyCountryCode\":null,\"secondNotifyProvince\":null,\"secondNotifyProvinceCode\":null,\"secondNotifyCity\":null,\"secondNotifyCityCode\":null,\"secondNotifyPostcode\":null},\"bookingBaseInfoBean\":{\"delegationNo\":\"***************\",\"billNo\":\"QD123456\",\"shippingCompanyCode\":\"MSC\",\"signingMethod\":\"OBL\",\"billFNum\":4,\"billSNum\":3,\"offSiteMark\":null,\"offSitePlace\":\"\",\"offSitePlaceCode\":\"\",\"paymentMethod\":\"FP\",\"paymentPlaceCode\":\"\",\"paymentPlace\":\"\",\"transportClause\":\"CY_CY\",\"contractNo\":\"asd123\",\"mrCodeFront\":null,\"mrCodeEnd\":null,\"oneselfSendingFlag\":null,\"scacCode\":null,\"aci\":null,\"house\":null,\"nameAccount\":null,\"bookingRemark\":null,\"motNo\":null,\"motNoMark\":null},\"sailingScheduleInfoBean\":{\"vessel\":\"MSK\",\"voyageNo\":\"123\",\"routeCode\":\"AS-1994\",\"etd\":\"2025-08-30\",\"placeOfReceipt\":\"QINGDAO\",\"placeOfReceiptCode\":\"CNTAO\",\"portOfLoading\":\"QINGDAO\",\"portOfLoadingCode\":\"CNTAO\",\"portOfTranshipment\":null,\"portOfTranshipmentCode\":null,\"portOfDischarge\":\"ZWONITZ\",\"portOfDischargeCode\":\"DEZWZ\",\"placeOfDelivery\":\"DARWAZ\",\"placeOfDeliveryCode\":\"AFDAZ\",\"destination\":null,\"destinationCode\":null,\"placeOfDeliveryVoyageLine\":\"中东印巴线\",\"placeOfDeliveryVoyageLineCode\":\"CNMEL\"},\"cargoInfoBeanList\":[{\"genericType\":true,\"coldStorageType\":null,\"dangerType\":null,\"oogType\":null,\"chemicalsType\":null,\"marks\":\"ASD\",\"englishProductName\":\"ASDF\",\"chineseProductName\":\"ZHEI SHI YI GE CE SHI PIN MING\",\"number\":1,\"packingUnit\":\"CK\",\"packageUnitNameEn\":\"Cask\",\"packageUnitNameCn\":\"木桶\",\"grossWeight\":2,\"netWeight\":null,\"volume\":1,\"hsCode\":\"123456\",\"temperature\":null,\"temperatureUnit\":null,\"maximumTemperature\":null,\"minimumTemperature\":null,\"ventSwitch\":null,\"ventilationRate\":null,\"dangerClass\":null,\"dangerUnCode\":null,\"urgentContactPerson\":\"\",\"urgentContactPhone\":\"\",\"page\":null,\"dangerLabel\":null,\"dangerFlashPoint\":null,\"dangerEmn\":null,\"megn\":null,\"marinePollution\":null,\"casNo\":null,\"subClass\":null,\"properShippingName\":null,\"domesticWarehouseName\":null,\"frontBeyond\":null,\"retralBeyond\":null,\"leftBeyond\":null,\"rightBeyond\":null,\"heightBeyond\":null,\"oggUnit\":\"CM\"}],\"containerInfoBeanList\":[{\"containerType\":\"20GP\",\"containerTeu\":1,\"containerNumber\":1,\"soc\":true,\"ownersContainerType\":null,\"containerMark\":\"FULL\",\"avgGrossWeight\":\"1\"}],\"additionalInfoBean\":{\"destinationProperty\":\"BARGE_TERMINAL\",\"destinationTransportationType\":\"BARGE_TRUCK\"},\"contactInfoBean\":{\"contactPerson\":\"hgj_9975903\",\"phone\":\"13057539668\",\"qq\":null,\"emails\":\"<EMAIL>;<EMAIL>;<EMAIL>\"},\"bookingOtherInfoBean\":{\"bookingNo\":\"\",\"poNo\":null,\"shippingCompanySales\":null,\"freightRateDesc\":null},\"bookingFileBeans\":null},\"version\":7,\"mergeFlag\":\"MASTER\",\"supplierId\":\"\",\"carrierSupplierId\":\"776388504166146\",\"createTime\":\"2025-08-06 10:45:18\",\"enterpriseId\":\"1485915018826833922\",\"enterpriseName\":\"海管家订舱平台（演示）\",\"bookingType\":\"APPOINTMENT_NUM\"}",
    "recordId": "1133408733664768",
    "containerTypes": [
        {
            "containerName": "GP",
            "containerSize": "20",
            "standardCode": "22G1",
            "containerCode": "20GP"
        }
    ],
    "bookingExtendData": {
        "placeOfDeliveryVoyageLine": None,
        "placeOfDeliveryVoyageLineName": None,
        "bookingOfficePortCode": "CNTAO",
        "bookingOfficePortNameEn": "QINGDAO",
        "hmmSpecialAreaFlag": None,
        "commitDraft": True,
        "nonOperatingReefer": None
    },
    "accountBean": {
        "account": "***********",
        "password": "905eee89ecba8d076d95a2f8fd74581a"
    },
    "orderFiles": [
        {
            "fileUrl": "https://iter-apisix.hgj.com/whale-nas-manager-server/pass/proxy/1953730885888974849",
            "fileName": "***************青岛外运箱满舱模板.xlsx"
        }
    ],
    "messageId":str(int(time.time() * 1000)),
    "count":1
}
    msg["detail"] = json.loads(msg["detail"])
    while True:
        browser, service = selenium_obj.create_browser()
        q = QDWYSpider(msg, service, browser)
        q.main()
